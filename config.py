#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

# 飞书应用配置
FEISHU_CONFIG = {
    "app_id": "",  # 在飞书开放平台创建应用后获得
    "app_secret": "",  # 应用密钥
    "app_token": "",  # 多维表格的app_token
    "table_id": ""  # 表格ID（创建表格后获得）
}

# AI模型配置
AI_CONFIG = {
    # OpenAI配置
    "openai": {
        "api_key": "",
        "model": "gpt-4-vision-preview",
        "max_tokens": 1000
    },
    
    # 百度文心一言配置（备选）
    "baidu": {
        "api_key": "",
        "secret_key": "",
        "model": "ERNIE-Bot-4"
    },
    
    # 阿里通义千问配置（备选）
    "alibaba": {
        "api_key": "",
        "model": "qwen-vl-plus"
    }
}

# 应用设置
APP_CONFIG = {
    "supported_formats": [".jpg", ".jpeg", ".png", ".pdf"],
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "batch_size": 5,  # 批量处理时每批的数量
    "retry_times": 3,  # API调用失败重试次数
    "timeout": 30  # API调用超时时间（秒）
}

# 飞书表格字段映射
FEISHU_FIELDS = {
    "ticket_details": {
        "票据ID": {"type": "text", "required": True},
        "供应商": {"type": "text", "required": True},
        "日期": {"type": "date", "required": True},
        "类型": {"type": "select", "options": ["销售", "退货"], "required": True},
        "款号": {"type": "text", "required": True},
        "颜色规格": {"type": "text", "required": False},
        "数量": {"type": "number", "required": True},
        "单价": {"type": "number", "required": True},
        "小计": {"type": "number", "required": True},
        "处理时间": {"type": "datetime", "required": True},
        "状态": {"type": "select", "options": ["待审核", "已确认", "有问题"], "required": True}
    },
    
    "cost_comparison": {
        "款号": {"type": "text", "required": True},
        "ERP成本价": {"type": "number", "required": False},
        "票据价格": {"type": "number", "required": True},
        "差异": {"type": "number", "required": False},
        "差异率": {"type": "number", "required": False},
        "状态": {"type": "select", "options": ["缺失", "不一致", "正常"], "required": True},
        "更新时间": {"type": "datetime", "required": True}
    }
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "ticket_processor.log",
    "max_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 5
}
