#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模型配置文件
包含各种AI模型的API配置信息和使用说明
"""

# AI模型预设配置
AI_MODELS_CONFIG = {
    "openai": {
        "name": "OpenAI GPT-4V",
        "api_url": "https://api.openai.com/v1/chat/completions",
        "model": "gpt-4-vision-preview",
        "description": "OpenAI的多模态大模型，识别准确率高",
        "auth_type": "Bearer",
        "headers": {
            "Content-Type": "application/json",
            "Authorization": "Bearer {api_key}"
        },
        "payload_template": {
            "model": "gpt-4-vision-preview",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "{prompt}"},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": "data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 1000,
            "temperature": 0.1
        },
        "response_path": ["choices", 0, "message", "content"]
    },
    
    "baidu": {
        "name": "百度文心一言",
        "api_url": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro",
        "model": "ERNIE-Bot-4",
        "description": "百度的多模态大模型，支持中文优化",
        "auth_type": "access_token",
        "headers": {
            "Content-Type": "application/json"
        },
        "payload_template": {
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "{prompt}"},
                        {
                            "type": "image_url",
                            "image_url": "data:image/jpeg;base64,{image_base64}"
                        }
                    ]
                }
            ],
            "temperature": 0.1,
            "top_p": 0.8,
            "penalty_score": 1.0
        },
        "response_path": ["result"],
        "note": "需要先获取access_token，URL需要添加?access_token=YOUR_ACCESS_TOKEN"
    },
    
    "alibaba": {
        "name": "阿里通义千问VL",
        "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
        "model": "qwen-vl-plus",
        "description": "阿里云的多模态大模型",
        "auth_type": "Authorization",
        "headers": {
            "Content-Type": "application/json",
            "Authorization": "Bearer {api_key}"
        },
        "payload_template": {
            "model": "qwen-vl-plus",
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"text": "{prompt}"},
                            {"image": "data:image/jpeg;base64,{image_base64}"}
                        ]
                    }
                ]
            },
            "parameters": {
                "result_format": "message"
            }
        },
        "response_path": ["output", "choices", 0, "message", "content", 0, "text"]
    },
    
    "zhipu": {
        "name": "智谱GLM-4V",
        "api_url": "https://open.bigmodel.cn/api/paas/v4/chat/completions",
        "model": "glm-4v",
        "description": "智谱AI的多模态大模型",
        "auth_type": "Bearer",
        "headers": {
            "Content-Type": "application/json",
            "Authorization": "Bearer {api_key}"
        },
        "payload_template": {
            "model": "glm-4v",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "{prompt}"},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": "data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 1000,
            "temperature": 0.1
        },
        "response_path": ["choices", 0, "message", "content"]
    },
    
    "moonshot": {
        "name": "月之暗面Kimi",
        "api_url": "https://api.moonshot.cn/v1/chat/completions",
        "model": "moonshot-v1-8k",
        "description": "月之暗面的大模型（需要先OCR）",
        "auth_type": "Bearer",
        "headers": {
            "Content-Type": "application/json",
            "Authorization": "Bearer {api_key}"
        },
        "payload_template": {
            "model": "moonshot-v1-8k",
            "messages": [
                {
                    "role": "user",
                    "content": "{prompt}\n\n图像内容：{ocr_text}"
                }
            ],
            "temperature": 0.1
        },
        "response_path": ["choices", 0, "message", "content"],
        "note": "此模型不支持图像输入，需要先进行OCR识别"
    }
}

# 默认提示词模板
DEFAULT_PROMPT_TEMPLATES = {
    "standard": """你是一名专业的中文服饰票据信息抽取助手。请分析以下票据图像，提取关键信息并转换为标准JSON格式。

**分析要求：**
1. 仔细识别票据中的所有文字信息
2. 提取以下字段：
   - supplier：供应商名称（通常在票据顶部或印章处）
   - date：日期（统一格式为YYYY-MM-DD）
   - type：票据类型（任一商品数量或金额为负数则为"退货"，否则为"销售"）
   - items：商品明细数组，每项包含：
     * 款号：商品编号/货号
     * 颜色规格：颜色、尺码等规格信息
     * 数量：商品数量（退货为负数）
     * 单价：单位价格
     * 小计：该行总金额（数量×单价）

**注意事项：**
- 自动纠正识别错误（如"状号"→"款号"）
- 合理推断缺失信息（根据上下文判断供应商、日期）
- 确保数值计算正确（小计=数量×单价）
- 严格按JSON格式输出，不包含其他文字

请输出标准JSON结果：""",

    "detailed": """你是一名专业的票据信息抽取专家，专门处理中文服饰类票据。请仔细分析提供的票据图像。

**任务目标：**
从票据中提取结构化信息，输出标准JSON格式数据。

**提取字段说明：**
1. supplier（供应商）：
   - 查找票据顶部、抬头、印章处的公司名称
   - 可能出现在"销售方"、"开票方"等标识附近
   
2. date（日期）：
   - 统一转换为YYYY-MM-DD格式
   - 常见格式：2024年1月15日 → 2024-01-15
   
3. type（票据类型）：
   - 检查所有商品的数量和金额
   - 如有任一项为负数，则为"退货"
   - 否则为"销售"
   
4. items（商品明细）：
   每个商品包含：
   - 款号：商品编号、货号、SKU等
   - 颜色规格：颜色、尺码、规格描述
   - 数量：商品数量（整数）
   - 单价：单位价格（保留2位小数）
   - 小计：行总金额（数量×单价）

**处理规则：**
- 自动纠正OCR错误（如"状号"应为"款号"）
- 智能推断缺失信息
- 验证数值计算准确性
- 处理表格格式的商品明细

**输出要求：**
仅输出JSON格式结果，不包含任何解释文字。

JSON结构：
{
  "supplier": "供应商名称",
  "date": "YYYY-MM-DD",
  "type": "销售/退货",
  "items": [
    {
      "款号": "商品编号",
      "颜色规格": "规格描述",
      "数量": 数量,
      "单价": 单价,
      "小计": 小计金额
    }
  ]
}""",

    "simple": """请分析这张票据图像，提取商品信息并输出JSON格式：

需要提取：
- supplier: 供应商名称
- date: 日期(YYYY-MM-DD格式)
- type: "销售"或"退货"(有负数则为退货)
- items: 商品列表，包含款号、颜色规格、数量、单价、小计

只输出JSON，不要其他文字："""
}

# 使用说明
USAGE_INSTRUCTIONS = {
    "openai": {
        "setup": [
            "1. 访问 https://platform.openai.com/",
            "2. 创建账户并获取API Key",
            "3. 确保账户有GPT-4V访问权限",
            "4. 将API Key填入配置"
        ],
        "cost": "按token计费，图像处理成本较高",
        "limits": "有请求频率限制"
    },
    
    "baidu": {
        "setup": [
            "1. 访问百度智能云控制台",
            "2. 开通文心一言服务",
            "3. 创建应用获取API Key和Secret Key",
            "4. 使用API Key和Secret Key获取access_token"
        ],
        "cost": "按调用次数计费",
        "limits": "有QPS限制"
    },
    
    "alibaba": {
        "setup": [
            "1. 访问阿里云控制台",
            "2. 开通通义千问服务",
            "3. 创建API Key",
            "4. 配置API Key到系统"
        ],
        "cost": "按token计费",
        "limits": "有并发限制"
    }
}

def get_model_config(model_name: str) -> dict:
    """获取指定模型的配置"""
    return AI_MODELS_CONFIG.get(model_name, {})

def get_all_models() -> list:
    """获取所有可用模型列表"""
    return list(AI_MODELS_CONFIG.keys())

def get_model_names() -> list:
    """获取所有模型的显示名称"""
    return [config["name"] for config in AI_MODELS_CONFIG.values()]
