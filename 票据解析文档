背景
用户需要解析大量格式各异的中文服饰类票据，这些票据由不同供应商提供，包含商品款号、颜色/规格、数量、单价、小计金额等信息。其中有些是销售票据，有些是退货票据（通常以负数表示退货项目）。传统OCR提取的文字是不结构化的，需要借助大语言模型将其转换为统一的结构化JSON数据，方便后续的存储和分析
blog.csdn.net
。大模型可以利用其强大的语义理解和信息抽取能力，对OCR结果进行组织并纠正识别错误
cnblogs.com
。我们的目标是编写一个通用提示词模板，指导模型从杂乱的票据OCR文本中准确提取关键信息，并输出规范的JSON格式结果。
目标JSON结构
解析后的JSON需包含票据信息的主要字段，结构如下所示：
json
复制
编辑
{
  "supplier": "供应商名称",
  "date": "YYYY-MM-DD",
  "type": "销售",
  "items": [
    {
      "款号": "",
      "颜色规格": "",
      "数量": 0,
      "单价": 0.0,
      "小计": 0.0
    },
    ...
  ]
}
上述JSON各字段含义：
supplier：供应商名称（票据抬头或其它位置出现的公司/店铺名）。
date：日期（需统一为YYYY-MM-DD格式，即年-月-日）。票据中日期可能有多种格式（例如“2023年5月6日”或2023/05/06），模型应规范化输出。
type：票据类型，取值为"销售"或"退货"。判断逻辑：如果票据中存在任何商品数量或金额为负数，则视为退货票据，输出"退货"；否则为销售票据，输出"销售"
m.kuaiji.com
。这是因为负数通常用于标识退货或冲销项目
m.kuaiji.com
。
items：商品明细列表，每个元素为一个物品的记录，包括：
款号：商品款号（商品的型号或货号）。
颜色规格：商品的颜色或规格描述。
数量：商品数量（整数，退货情况下可能为负数以表示退回数量）。
单价：商品单价（数字，单位价格）。
小计：该商品行的小计金额（数字，等于数量乘以单价，退货则为负数）。
提示词设计考量
编写提示词时，需要考虑票据文本的多样性和OCR识别可能出现的问题，确保大模型充分利用上下文准确提取信息并格式化输出JSON：
明确JSON输出要求：提示词中应当清晰要求模型以标准JSON格式输出结果，且只输出JSON而不包含额外说明。这种明确指令有助于模型稳定地给出结构化结果
dev.to
。例如，在提示中加入类似“请将结果转换为JSON格式输出”的语句，可提高JSON输出的一致性
dev.to
。
票据类型判断：提示模型根据数值正负判断“销售”或“退货”。如果OCR文本中发现数量或金额带有负号“-”，提示模型将type字段设为“退货”；否则为“销售”。这一规则源自业务惯例：负数金额的发票通常表示退货或冲红处理
m.kuaiji.com
。
字段提取和上下文推断：指导模型提取供应商名称、日期等全局信息。如果这些字段未明确标注（例如没有直接出现“供应商：”或“日期：”标签），提示模型根据上下文推断：供应商名称往往出现在票据顶部或盖章处，日期可能出现在票据顶部或明细表格附近等。要求模型尽可能从文本上下文中识别或推断出这些信息，即使OCR结果未明显标记。比如，票据抬头、商家信息栏或二维码旁的文字可能就是供应商名称或日期。
纠正OCR识别错误：OCR可能将一些字段识别错误（例如将“款号”误识别为“状号”）。在提示词中应说明允许模型结合上下文进行纠正
cnblogs.com
。大模型可以根据语义判断出不合常理的识别结果并加以修正
cnblogs.com
。例如，提示模型：“如果发现OCR文本中出现与已知字段名称相近但有误的词语，请自动更正（如识别出的‘状号’应校正为‘款号’）”。同样，对于因扫描造成的错位或分隔错误，模型应通过语义理解重新组织信息
bilibili.com
。
适配多平台的大模型：提示词用语应简明扼要、措辞准确，使用中文描述，以确保在不同的大模型（如GPT-4、文心一言、通义千问等）上都能被正确理解。避免使用特定平台的专有格式或功能（如函数调用等），而采用通用的指令式语言，使各模型都能按照要求执行。
以上考量确保提示词既上下文丰富，涵盖必要的业务规则，又能精确指令模型正确解析复杂票据文本，并生成所需JSON结果。接下来，我们根据这些原则构建最终的提示词模板。
通用提示词模板
以下是一份可用于中文票据OCR解析的通用提示词模板。将识别出的票据文本放入指定位置，并对模型进行提问：
text
复制
编辑
你是一名智能票据信息抽取助手。现在我将提供一段服饰类票据的OCR识别文本，请你将其中关键信息提取并转换为结构化的JSON格式数据。

**票据OCR文本：**
<<在此粘贴OCR识别后的票据全文>>

**提取要求：**  
1. 根据上述票据文本，提取以下字段并填写JSON：
   - supplier：供应商名称（公司或店铺名）。  
   - date：日期（格式统一为YYYY-MM-DD）。  
   - type：票据类型，若任一商品的数量或金额为负数则视为**退货**，否则为**销售**。  
   - items：商品明细列表，每个商品包含“款号”、“颜色规格”、“数量”、“单价”、“小计”。数量和小计如为退货应为负数。  
2. 注意纠正OCR可能出现的识别错误。例如，字段名“款号”若被错误识别为类似“状号”等，应更正为“款号”。同理，识别出的文本如果有错别字或不合理断行，请结合上下文修正。  
3. 如果票据中未直接标出“供应商”或“日期”，请根据票据抬头、印章或上下文内容合理推断并填写。确保所有输出信息来源于票据内容。  
4. 输出**严格符合JSON格式**，不要包含额外说明文字。仅输出一个JSON对象。

请根据上述要求解析票据文本，并仅输出提取完成的JSON结果：
在实际使用时，将上述模板中的<<在此粘贴OCR识别后的票据全文>>替换为具体票据的OCR文本。提示词已经明确规定了输出JSON的结构和提取准则，模型会依据这些指令进行分析并给出结果
dev.to
。通过这样的精心设计，即使输入的票据格式复杂、内容杂乱，模型也能理解上下文并准确提取信息，生成统一规范的JSON结构输出。