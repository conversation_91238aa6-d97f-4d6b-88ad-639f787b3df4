#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的GUI界面
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from gui_app import TicketProcessorGUI
    
    def main():
        print("启动飞书票据处理系统...")
        print("新功能:")
        print("- ✅ 去掉了APP TOKEN配置")
        print("- ✅ 去掉了AI模型预设按钮")
        print("- ✅ 新增带+号的上传框")
        print("- ✅ 支持点击上传框选择文件")
        print("- ✅ 支持Ctrl+V粘贴图像")
        print("- ✅ 实时图像预览")
        print("- ✅ 可以单独删除每个图像")
        print("")
        
        app = TicketProcessorGUI()
        app.run()
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有依赖包已安装:")
    print("pip install -r requirements.txt")
except Exception as e:
    print(f"启动失败: {e}")
    import traceback
    traceback.print_exc()
