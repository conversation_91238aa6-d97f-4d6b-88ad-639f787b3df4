#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书票据处理系统使用示例
演示如何使用不同的AI模型进行票据识别
"""

from feishu_ticket_processor import FeishuTicketProcessor
from ai_models_config import AI_MODELS_CONFIG, DEFAULT_PROMPT_TEMPLATES

def example_openai():
    """使用OpenAI GPT-4V的示例"""
    print("=== OpenAI GPT-4V 示例 ===")
    
    # 配置信息
    feishu_app_id = "your_feishu_app_id"
    feishu_app_secret = "your_feishu_app_secret"
    openai_api_key = "your_openai_api_key"
    openai_api_url = "https://api.openai.com/v1/chat/completions"
    
    # 创建处理器
    processor = FeishuTicketProcessor(
        app_id=feishu_app_id,
        app_secret=feishu_app_secret,
        ai_api_key=openai_api_key,
        ai_api_url=openai_api_url,
        prompt_template=DEFAULT_PROMPT_TEMPLATES["standard"]
    )
    
    # 处理票据
    app_token = "your_app_token"
    table_id = "your_table_id"
    image_path = "ticket_example.jpg"
    
    success = processor.process_ticket(image_path, app_token, table_id)
    print(f"处理结果: {'成功' if success else '失败'}")

def example_baidu():
    """使用百度文心一言的示例"""
    print("=== 百度文心一言 示例 ===")
    
    # 配置信息
    feishu_app_id = "your_feishu_app_id"
    feishu_app_secret = "your_feishu_app_secret"
    baidu_api_key = "your_baidu_api_key"
    baidu_api_url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro?access_token=YOUR_ACCESS_TOKEN"
    
    # 创建处理器
    processor = FeishuTicketProcessor(
        app_id=feishu_app_id,
        app_secret=feishu_app_secret,
        ai_api_key=baidu_api_key,
        ai_api_url=baidu_api_url,
        prompt_template=DEFAULT_PROMPT_TEMPLATES["detailed"]
    )
    
    # 处理票据
    app_token = "your_app_token"
    table_id = "your_table_id"
    image_path = "ticket_example.jpg"
    
    success = processor.process_ticket(image_path, app_token, table_id)
    print(f"处理结果: {'成功' if success else '失败'}")

def example_alibaba():
    """使用阿里通义千问的示例"""
    print("=== 阿里通义千问 示例 ===")
    
    # 配置信息
    feishu_app_id = "your_feishu_app_id"
    feishu_app_secret = "your_feishu_app_secret"
    alibaba_api_key = "your_alibaba_api_key"
    alibaba_api_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"
    
    # 创建处理器
    processor = FeishuTicketProcessor(
        app_id=feishu_app_id,
        app_secret=feishu_app_secret,
        ai_api_key=alibaba_api_key,
        ai_api_url=alibaba_api_url,
        prompt_template=DEFAULT_PROMPT_TEMPLATES["simple"]
    )
    
    # 处理票据
    app_token = "your_app_token"
    table_id = "your_table_id"
    image_path = "ticket_example.jpg"
    
    success = processor.process_ticket(image_path, app_token, table_id)
    print(f"处理结果: {'成功' if success else '失败'}")

def example_batch_processing():
    """批量处理示例"""
    print("=== 批量处理示例 ===")
    
    # 配置信息
    feishu_app_id = "your_feishu_app_id"
    feishu_app_secret = "your_feishu_app_secret"
    ai_api_key = "your_ai_api_key"
    ai_api_url = "https://api.openai.com/v1/chat/completions"
    
    # 创建处理器
    processor = FeishuTicketProcessor(
        app_id=feishu_app_id,
        app_secret=feishu_app_secret,
        ai_api_key=ai_api_key,
        ai_api_url=ai_api_url,
        prompt_template=DEFAULT_PROMPT_TEMPLATES["standard"]
    )
    
    # 批量处理多张票据
    app_token = "your_app_token"
    table_id = "your_table_id"
    image_paths = [
        "ticket1.jpg",
        "ticket2.jpg", 
        "ticket3.pdf",
        "ticket4.png"
    ]
    
    processor.batch_process_tickets(image_paths, app_token, table_id)

def example_custom_prompt():
    """自定义提示词示例"""
    print("=== 自定义提示词示例 ===")
    
    # 自定义提示词
    custom_prompt = """
请分析这张服饰票据，重点关注以下信息：

1. 供应商信息（公司名称）
2. 票据日期
3. 商品详情：
   - 款式编号
   - 颜色和尺码
   - 数量
   - 单价
   - 小计金额

请以JSON格式输出，格式如下：
{
  "supplier": "供应商名称",
  "date": "YYYY-MM-DD",
  "type": "销售/退货",
  "items": [
    {
      "款号": "编号",
      "颜色规格": "颜色 尺码",
      "数量": 数量,
      "单价": 价格,
      "小计": 金额
    }
  ]
}

注意：
- 如果有负数金额或数量，票据类型为"退货"
- 确保数值计算准确
- 只输出JSON，不要其他内容
"""
    
    # 配置信息
    feishu_app_id = "your_feishu_app_id"
    feishu_app_secret = "your_feishu_app_secret"
    ai_api_key = "your_ai_api_key"
    ai_api_url = "https://api.openai.com/v1/chat/completions"
    
    # 创建处理器
    processor = FeishuTicketProcessor(
        app_id=feishu_app_id,
        app_secret=feishu_app_secret,
        ai_api_key=ai_api_key,
        ai_api_url=ai_api_url,
        prompt_template=custom_prompt
    )
    
    # 处理票据
    app_token = "your_app_token"
    table_id = "your_table_id"
    image_path = "ticket_example.jpg"
    
    success = processor.process_ticket(image_path, app_token, table_id)
    print(f"处理结果: {'成功' if success else '失败'}")

def example_dynamic_config():
    """动态切换配置示例"""
    print("=== 动态切换配置示例 ===")
    
    # 创建处理器
    processor = FeishuTicketProcessor(
        app_id="your_feishu_app_id",
        app_secret="your_feishu_app_secret"
    )
    
    # 票据文件
    app_token = "your_app_token"
    table_id = "your_table_id"
    image_path = "ticket_example.jpg"
    
    # 尝试不同的AI模型
    models_to_try = [
        {
            "name": "OpenAI",
            "api_key": "your_openai_key",
            "api_url": "https://api.openai.com/v1/chat/completions",
            "prompt": DEFAULT_PROMPT_TEMPLATES["standard"]
        },
        {
            "name": "百度文心一言",
            "api_key": "your_baidu_key", 
            "api_url": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro?access_token=YOUR_TOKEN",
            "prompt": DEFAULT_PROMPT_TEMPLATES["detailed"]
        }
    ]
    
    for model_config in models_to_try:
        print(f"\n尝试使用 {model_config['name']} 模型...")
        
        # 更新AI配置
        processor.update_ai_config(
            api_key=model_config["api_key"],
            api_url=model_config["api_url"],
            prompt_template=model_config["prompt"]
        )
        
        # 处理票据
        success = processor.process_ticket(image_path, app_token, table_id)
        print(f"{model_config['name']} 处理结果: {'成功' if success else '失败'}")
        
        if success:
            print(f"使用 {model_config['name']} 处理成功，停止尝试其他模型")
            break

if __name__ == "__main__":
    print("飞书票据处理系统使用示例")
    print("请根据需要取消注释相应的示例函数")
    
    # 取消注释以运行相应示例
    # example_openai()
    # example_baidu()
    # example_alibaba()
    # example_batch_processing()
    # example_custom_prompt()
    # example_dynamic_config()
    
    print("\n注意：运行前请先配置正确的API密钥和飞书应用信息")
