# 飞书票据处理系统

基于AI模型的智能票据识别系统，自动提取票据信息并提交到飞书多维表格。

## 功能特点

- 🤖 **AI智能识别**：使用GPT-4V等多模态大模型直接识别票据图像
- 📊 **飞书集成**：自动将数据提交到飞书多维表格，支持团队协作
- 🎯 **统一格式**：标准化JSON输出，支持销售/退货票据类型判断
- 🖥️ **友好界面**：简洁的桌面GUI，无需技术背景即可使用
- 📁 **批量处理**：支持同时处理多张票据，提高工作效率

## 系统要求

- Python 3.8+
- Windows/macOS/Linux
- 网络连接（用于AI API调用）

## 安装步骤

### 1. 克隆项目
```bash
git clone <项目地址>
cd feishu-ticket-processor
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置飞书应用

#### 3.1 创建飞书应用
1. 访问 [飞书开放平台](https://open.feishu.cn/)
2. 创建企业自建应用
3. 获取 `App ID` 和 `App Secret`

#### 3.2 配置应用权限
在应用管理页面，添加以下权限：
- `bitable:app` - 多维表格应用权限
- `bitable:app:readonly` - 读取多维表格
- `bitable:app:readwrite` - 读写多维表格

#### 3.3 创建多维表格
1. 在飞书中创建新的多维表格
2. 获取表格的 `app_token`（URL中的标识）
3. 记录表格ID（可通过API获取或创建时返回）

### 4. 配置AI模型API

#### 4.1 OpenAI配置（推荐）
1. 访问 [OpenAI平台](https://platform.openai.com/)
2. 创建API Key
3. 确保账户有GPT-4V访问权限

#### 4.2 其他模型（可选）
- 百度文心一言：在百度智能云申请API
- 阿里通义千问：在阿里云申请API

## 使用方法

### 1. 启动应用
```bash
python gui_app.py
```

### 2. 配置设置
在GUI界面中填写：
- 飞书App ID
- 飞书App Secret  
- 表格App Token
- 表格ID
- OpenAI API Key

点击"保存配置"按钮保存设置。

### 3. 选择票据文件
- 点击"选择票据文件"按钮
- 支持格式：JPG、PNG、PDF
- 可以选择多个文件进行批量处理

### 4. 开始处理
- 点击"开始处理"按钮
- 系统会自动：
  1. 使用AI模型识别票据内容
  2. 提取结构化信息
  3. 提交到飞书表格
- 处理进度和日志会实时显示

## 数据格式

### 输入格式
支持的票据文件格式：
- 图像文件：JPG、JPEG、PNG
- PDF文件：PDF

### 输出格式
提取的数据包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| 票据ID | 文本 | 自动生成的唯一标识 |
| 供应商 | 文本 | 供应商名称 |
| 日期 | 日期 | 票据日期（YYYY-MM-DD格式） |
| 类型 | 单选 | 销售/退货 |
| 款号 | 文本 | 商品款号 |
| 颜色规格 | 文本 | 商品规格信息 |
| 数量 | 数字 | 商品数量 |
| 单价 | 数字 | 单位价格 |
| 小计 | 数字 | 行总金额 |
| 处理时间 | 日期时间 | 数据录入时间 |
| 状态 | 单选 | 待审核/已确认/有问题 |

## 高级功能

### 1. 命令行使用
```python
from feishu_ticket_processor import FeishuTicketProcessor

# 创建处理器
processor = FeishuTicketProcessor(
    app_id="your_app_id",
    app_secret="your_app_secret", 
    openai_api_key="your_openai_key"
)

# 处理单张票据
processor.process_ticket("ticket.jpg", "app_token", "table_id")

# 批量处理
ticket_files = ["ticket1.jpg", "ticket2.jpg"]
processor.batch_process_tickets(ticket_files, "app_token", "table_id")
```

### 2. 自定义提示词
可以修改 `feishu_ticket_processor.py` 中的 `prompt_template` 来优化识别效果。

### 3. 多模型支持
系统设计支持多种AI模型，可以扩展支持：
- 百度文心一言
- 阿里通义千问
- 其他多模态大模型

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查网络连接
   - 验证API Key是否正确
   - 确认API额度是否充足

2. **飞书权限错误**
   - 检查应用权限配置
   - 确认app_token和table_id正确
   - 验证应用是否已发布

3. **识别准确率低**
   - 确保票据图像清晰
   - 尝试调整图像大小和格式
   - 优化提示词模板

4. **文件格式不支持**
   - 确认文件格式在支持列表中
   - 检查文件是否损坏
   - 尝试转换文件格式

### 日志查看
- GUI界面实时显示处理日志
- 详细日志保存在 `ticket_processor.log` 文件中

## 开发说明

### 项目结构
```
├── feishu_ticket_processor.py  # 核心处理逻辑
├── gui_app.py                  # GUI界面
├── config.py                   # 配置文件
├── requirements.txt            # 依赖包列表
├── README.md                   # 使用说明
└── user_config.json           # 用户配置（自动生成）
```

### 扩展开发
- 添加新的AI模型支持
- 自定义票据模板
- 集成更多数据源
- 开发Web版本

## 许可证

MIT License

## 支持

如有问题或建议，请联系开发团队。
