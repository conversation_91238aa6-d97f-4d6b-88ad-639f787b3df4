# 飞书票据处理系统

基于AI模型的智能票据识别系统，自动提取票据信息并提交到飞书多维表格。

## 功能特点

- 🤖 **多模型支持**：支持OpenAI GPT-4V、百度文心一言、阿里通义千问等多种AI模型
- 🔧 **灵活配置**：支持自定义API Key和URL，可快速切换不同AI服务
- ✏️ **可视化编辑**：提示词可视化编辑，支持针对不同场景优化识别效果
- 📋 **便捷输入**：支持文件选择和剪贴板粘贴图像，Ctrl+V快速添加票据
- 🖼️ **图像预览**：内置图像预览功能，确认票据内容后再处理
- 📊 **飞书集成**：自动将数据提交到飞书多维表格，支持团队协作
- 🎯 **统一格式**：标准化JSON输出，支持销售/退货票据类型判断
- 🖥️ **友好界面**：简洁的桌面GUI，无需技术背景即可使用
- 📁 **批量处理**：支持同时处理多张票据，提高工作效率
- 🔄 **热切换**：支持运行时切换AI模型，无需重启应用

## 系统要求

- Python 3.8+
- Windows/macOS/Linux
- 网络连接（用于AI API调用）

## 安装步骤

### 1. 克隆项目
```bash
git clone <项目地址>
cd feishu-ticket-processor
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置飞书应用

#### 3.1 创建飞书应用
1. 访问 [飞书开放平台](https://open.feishu.cn/)
2. 创建企业自建应用
3. 获取 `App ID` 和 `App Secret`

#### 3.2 配置应用权限
在应用管理页面，添加以下权限：
- `bitable:app` - 多维表格应用权限
- `bitable:app:readonly` - 读取多维表格
- `bitable:app:readwrite` - 读写多维表格

#### 3.3 创建多维表格
1. 在飞书中创建新的多维表格
2. 获取表格的 `app_token`（URL中的标识）
3. 记录表格ID（可通过API获取或创建时返回）

### 4. 配置AI模型API

系统支持多种AI模型，您可以根据需要选择：

#### 4.1 OpenAI GPT-4V（推荐）
1. 访问 [OpenAI平台](https://platform.openai.com/)
2. 创建API Key
3. 确保账户有GPT-4V访问权限
4. API URL: `https://api.openai.com/v1/chat/completions`

#### 4.2 百度文心一言
1. 访问 [百度智能云](https://cloud.baidu.com/)
2. 开通文心一言服务
3. 获取API Key和Secret Key
4. API URL: `https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro`

#### 4.3 阿里通义千问
1. 访问 [阿里云控制台](https://dashscope.console.aliyun.com/)
2. 开通通义千问服务
3. 创建API Key
4. API URL: `https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation`

#### 4.4 智谱GLM-4V
1. 访问 [智谱AI平台](https://open.bigmodel.cn/)
2. 注册并获取API Key
3. API URL: `https://open.bigmodel.cn/api/paas/v4/chat/completions`

#### 4.5 自定义模型
系统支持任何兼容OpenAI格式的API接口，您可以：
- 使用本地部署的大模型
- 使用其他第三方AI服务
- 自定义API格式

## 使用方法

### 1. 启动应用
```bash
python gui_app.py
```

### 2. 配置设置
在GUI界面中填写：
- **飞书配置**：
  - 飞书App ID
  - 飞书App Secret
  - 表格App Token
  - 表格ID
- **AI模型配置**：
  - AI API Key
  - AI API URL

#### 2.1 快速配置AI模型
点击预设按钮快速配置常用AI模型：
- **OpenAI**：自动设置OpenAI API地址
- **文心一言**：自动设置百度API地址
- **通义千问**：自动设置阿里云API地址
- **自定义**：手动输入API地址

#### 2.2 编辑提示词
- 点击"编辑提示词"按钮打开提示词编辑器
- 可以自定义提示词以优化识别效果
- 支持重置为默认提示词

点击"保存配置"按钮保存所有设置。

### 3. 选择票据文件
支持多种方式添加票据文件：

#### 3.1 文件选择
- 点击"选择票据文件"按钮
- 支持格式：JPG、PNG、PDF
- 可以选择多个文件进行批量处理

#### 3.2 粘贴图像（新功能）
- **快捷键**：按 `Ctrl+V` 直接粘贴剪贴板中的图像
- **按钮操作**：点击"粘贴图像"按钮
- **使用场景**：
  - 从网页、微信、QQ等复制图像后直接粘贴
  - 截图后直接粘贴使用
  - 从其他应用复制的图像内容

#### 3.3 图像预览
- 点击"预览图像"按钮查看已选择的图像
- 支持缩略图预览，确认图像内容
- 区分文件来源：📁 表示文件选择，📋 表示剪贴板粘贴

#### 3.4 文件管理
- 点击"清空列表"清除所有文件（自动清理临时文件）
- 文件列表显示所有待处理的票据

### 4. 开始处理
- 点击"开始处理"按钮
- 系统会自动：
  1. 使用AI模型识别票据内容
  2. 提取结构化信息
  3. 提交到飞书表格
- 处理进度和日志会实时显示

## 数据格式

### 输入格式
支持的票据文件格式：
- 图像文件：JPG、JPEG、PNG
- PDF文件：PDF

### 输出格式
提取的数据包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| 票据ID | 文本 | 自动生成的唯一标识 |
| 供应商 | 文本 | 供应商名称 |
| 日期 | 日期 | 票据日期（YYYY-MM-DD格式） |
| 类型 | 单选 | 销售/退货 |
| 款号 | 文本 | 商品款号 |
| 颜色规格 | 文本 | 商品规格信息 |
| 数量 | 数字 | 商品数量 |
| 单价 | 数字 | 单位价格 |
| 小计 | 数字 | 行总金额 |
| 处理时间 | 日期时间 | 数据录入时间 |
| 状态 | 单选 | 待审核/已确认/有问题 |

## 高级功能

### 1. 命令行使用
```python
from feishu_ticket_processor import FeishuTicketProcessor

# 创建处理器
processor = FeishuTicketProcessor(
    app_id="your_app_id",
    app_secret="your_app_secret", 
    openai_api_key="your_openai_key"
)

# 处理单张票据
processor.process_ticket("ticket.jpg", "app_token", "table_id")

# 批量处理
ticket_files = ["ticket1.jpg", "ticket2.jpg"]
processor.batch_process_tickets(ticket_files, "app_token", "table_id")
```

### 2. 自定义提示词
可以修改 `feishu_ticket_processor.py` 中的 `prompt_template` 来优化识别效果。

### 3. 多模型支持
系统设计支持多种AI模型，可以扩展支持：
- 百度文心一言
- 阿里通义千问
- 其他多模态大模型

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查网络连接
   - 验证API Key是否正确
   - 确认API额度是否充足

2. **飞书权限错误**
   - 检查应用权限配置
   - 确认app_token和table_id正确
   - 验证应用是否已发布

3. **识别准确率低**
   - 确保票据图像清晰
   - 尝试调整图像大小和格式
   - 优化提示词模板

4. **文件格式不支持**
   - 确认文件格式在支持列表中
   - 检查文件是否损坏
   - 尝试转换文件格式

### 日志查看
- GUI界面实时显示处理日志
- 详细日志保存在 `ticket_processor.log` 文件中

## 开发说明

### 项目结构
```
├── feishu_ticket_processor.py  # 核心处理逻辑
├── gui_app.py                  # GUI界面
├── config.py                   # 配置文件
├── requirements.txt            # 依赖包列表
├── README.md                   # 使用说明
└── user_config.json           # 用户配置（自动生成）
```

### 扩展开发
- 添加新的AI模型支持
- 自定义票据模板
- 集成更多数据源
- 开发Web版本

## 许可证

MIT License

## 支持

如有问题或建议，请联系开发团队。
