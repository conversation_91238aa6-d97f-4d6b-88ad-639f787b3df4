# 飞书票据处理系统 - 使用技巧

## 📋 图像粘贴功能详解

### 快速粘贴操作
1. **截图粘贴**：
   - 使用 `Win + Shift + S` 截图
   - 在应用中按 `Ctrl + V` 直接粘贴
   - 或点击"粘贴图像"按钮

2. **复制图像粘贴**：
   - 从网页、微信、QQ等应用复制图像
   - 在应用中按 `Ctrl + V` 粘贴
   - 支持大部分图像格式

3. **文件复制粘贴**：
   - 在文件管理器中复制图像文件
   - 在应用中粘贴（会自动处理）

### 粘贴图像的优势
- ⚡ **快速便捷**：无需保存文件，直接处理
- 🔄 **实时处理**：截图后立即识别
- 💾 **自动管理**：临时文件自动清理
- 📱 **移动友好**：适合处理手机拍摄的票据

## 🎯 提示词优化技巧

### 针对不同票据类型优化

#### 1. 服饰类票据（默认）
```
你是一名专业的中文服饰票据信息抽取助手...
```

#### 2. 电子产品票据
```
你是一名专业的电子产品票据信息抽取助手。请分析以下票据图像，提取关键信息并转换为标准JSON格式。

**分析要求：**
1. 重点识别：
   - 产品型号、规格参数
   - 品牌信息
   - 序列号/IMEI等
   - 保修信息

**注意事项：**
- 电子产品型号通常包含字母数字组合
- 注意区分型号和序列号
- 价格可能包含税费信息
```

#### 3. 食品类票据
```
你是一名专业的食品票据信息抽取助手。请分析以下票据图像，提取关键信息并转换为标准JSON格式。

**分析要求：**
1. 重点识别：
   - 食品名称、规格
   - 生产日期、保质期
   - 批次号
   - 重量/数量单位

**注意事项：**
- 注意食品安全相关信息
- 重量单位可能是kg、g、ml等
- 可能包含营养成分信息
```

### 提示词调优建议
1. **明确字段要求**：详细说明每个字段的含义
2. **提供示例**：给出期望的JSON格式示例
3. **错误纠正**：指导AI纠正常见OCR错误
4. **上下文推理**：引导AI根据上下文推断信息

## 🔧 AI模型选择建议

### 模型特点对比

| 模型 | 优势 | 适用场景 | 成本 |
|------|------|----------|------|
| OpenAI GPT-4V | 识别准确率最高 | 复杂票据、高精度要求 | 较高 |
| 百度文心一言 | 中文优化好 | 中文票据、本土化需求 | 中等 |
| 阿里通义千问 | 性价比高 | 批量处理、成本敏感 | 较低 |
| 智谱GLM-4V | 响应速度快 | 实时处理需求 | 中等 |

### 选择建议
1. **高精度场景**：选择OpenAI GPT-4V
2. **中文票据**：优先百度文心一言
3. **批量处理**：选择阿里通义千问
4. **成本控制**：使用国产模型

## 📊 飞书表格优化

### 表格结构建议
1. **主表设计**：
   - 添加索引字段便于查找
   - 设置数据验证规则
   - 使用条件格式高亮异常数据

2. **视图配置**：
   - 按供应商分组
   - 按日期排序
   - 筛选特定类型票据

3. **权限管理**：
   - 设置不同角色权限
   - 保护重要字段不被误改
   - 记录操作日志

### 数据质量控制
1. **自动校验**：
   - 设置公式验证小计=数量×单价
   - 检查日期格式合理性
   - 标记异常数据

2. **人工审核**：
   - 定期检查识别结果
   - 标记需要复核的记录
   - 建立反馈机制

## 🚀 批量处理技巧

### 提高处理效率
1. **文件准备**：
   - 统一图像格式和大小
   - 确保图像清晰度
   - 按供应商分类整理

2. **批量操作**：
   - 一次选择多个文件
   - 使用粘贴功能快速添加
   - 合理安排处理时间

3. **错误处理**：
   - 记录处理失败的文件
   - 分析失败原因
   - 调整提示词或重新处理

### 性能优化
1. **网络环境**：
   - 确保网络稳定
   - 选择响应快的API服务
   - 避免高峰期处理

2. **系统资源**：
   - 关闭不必要的程序
   - 确保足够的内存空间
   - 定期清理临时文件

## 🔍 故障排除

### 常见问题解决

#### 1. 粘贴图像失败
- **检查剪贴板**：确保复制了图像而非文本
- **重启应用**：清理剪贴板状态
- **检查权限**：确保应用有访问剪贴板权限

#### 2. 识别准确率低
- **图像质量**：确保图像清晰、光线充足
- **提示词优化**：针对特定票据类型调整
- **模型切换**：尝试不同的AI模型

#### 3. 飞书提交失败
- **权限检查**：确认应用权限配置
- **网络连接**：检查网络状态
- **配置验证**：确认App Token和Table ID正确

#### 4. 临时文件占用空间
- **自动清理**：应用关闭时自动清理
- **手动清理**：点击"清空列表"按钮
- **定期维护**：定期清理系统临时文件夹

## 💡 最佳实践

### 工作流程建议
1. **准备阶段**：
   - 配置AI模型和飞书应用
   - 测试提示词效果
   - 准备票据文件

2. **处理阶段**：
   - 小批量测试
   - 检查识别结果
   - 批量处理

3. **验证阶段**：
   - 检查飞书表格数据
   - 人工验证关键信息
   - 标记异常记录

4. **优化阶段**：
   - 分析识别错误
   - 优化提示词
   - 调整处理流程

### 数据安全建议
1. **敏感信息**：
   - 避免在提示词中包含敏感信息
   - 定期清理临时文件
   - 使用本地模型处理机密票据

2. **备份策略**：
   - 定期导出飞书数据
   - 保留原始票据文件
   - 建立数据恢复机制

通过以上技巧，您可以更高效地使用飞书票据处理系统，提高工作效率和数据质量。
