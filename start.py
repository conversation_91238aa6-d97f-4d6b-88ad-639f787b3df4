#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书票据处理系统启动脚本
检查环境并启动GUI应用
"""

import sys
import os
import subprocess
import importlib.util

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误：需要Python 3.8或更高版本")
        print(f"当前版本：{sys.version}")
        return False
    print(f"✅ Python版本检查通过：{sys.version}")
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'lark_oapi',
        'requests', 
        'PIL',
        'pandas',
        'tkinter'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'tkinter':
                import tkinter
            else:
                importlib.import_module(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下依赖包：{', '.join(missing_packages)}")
        print("请运行以下命令安装：")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_config_file():
    """检查配置文件"""
    if os.path.exists("user_config.json"):
        print("✅ 找到配置文件：user_config.json")
        return True
    else:
        print("⚠️  未找到配置文件，首次使用请在GUI中配置")
        return True

def start_gui():
    """启动GUI应用"""
    try:
        print("\n🚀 启动飞书票据处理系统...")
        from gui_app import TicketProcessorGUI
        
        app = TicketProcessorGUI()
        app.run()
        
    except ImportError as e:
        print(f"❌ 导入错误：{e}")
        print("请确保所有文件都在正确位置")
        return False
    except Exception as e:
        print(f"❌ 启动失败：{e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🎯 飞书票据处理系统")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    print("\n📦 检查依赖包...")
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    print("\n⚙️  检查配置...")
    # 检查配置
    check_config_file()
    
    print("\n" + "=" * 50)
    
    # 启动GUI
    if not start_gui():
        input("按回车键退出...")

if __name__ == "__main__":
    main()
