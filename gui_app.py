#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书票据处理系统 - GUI界面
"""

import sys
import os
import json
from pathlib import Path
from typing import List
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
from datetime import datetime

from feishu_ticket_processor import FeishuTicketProcessor
from config import FEISHU_CONFIG, AI_CONFIG, APP_CONFIG

class TicketProcessorGUI:
    """票据处理系统GUI界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("飞书票据处理系统")
        self.root.geometry("800x600")
        
        # 初始化变量
        self.processor = None
        self.selected_files = []
        
        # 创建界面
        self.create_widgets()
        
        # 加载配置
        self.load_config()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置设置", padding="5")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 飞书配置
        ttk.Label(config_frame, text="飞书App ID:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.app_id_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.app_id_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        ttk.Label(config_frame, text="飞书App Secret:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        self.app_secret_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.app_secret_var, width=40, show="*").grid(row=1, column=1, sticky=(tk.W, tk.E))
        
        ttk.Label(config_frame, text="表格App Token:").grid(row=2, column=0, sticky=tk.W, padx=(0, 5))
        self.app_token_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.app_token_var, width=40).grid(row=2, column=1, sticky=(tk.W, tk.E))
        
        ttk.Label(config_frame, text="表格ID:").grid(row=3, column=0, sticky=tk.W, padx=(0, 5))
        self.table_id_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.table_id_var, width=40).grid(row=3, column=1, sticky=(tk.W, tk.E))
        
        # AI配置
        ttk.Label(config_frame, text="OpenAI API Key:").grid(row=4, column=0, sticky=tk.W, padx=(0, 5))
        self.openai_key_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.openai_key_var, width=40, show="*").grid(row=4, column=1, sticky=(tk.W, tk.E))
        
        # 保存配置按钮
        ttk.Button(config_frame, text="保存配置", command=self.save_config).grid(row=5, column=1, sticky=tk.E, pady=(5, 0))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="票据文件", padding="5")
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 文件选择按钮
        button_frame = ttk.Frame(file_frame)
        button_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        ttk.Button(button_frame, text="选择票据文件", command=self.select_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空列表", command=self.clear_files).pack(side=tk.LEFT, padx=(0, 5))
        
        # 文件列表
        self.file_listbox = tk.Listbox(file_frame, height=6)
        self.file_listbox.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(file_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        scrollbar.grid(row=1, column=2, sticky=(tk.N, tk.S))
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        # 处理控制区域
        control_frame = ttk.LabelFrame(main_frame, text="处理控制", padding="5")
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 处理按钮
        self.process_button = ttk.Button(control_frame, text="开始处理", command=self.start_processing)
        self.process_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="5")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        config_frame.columnconfigure(1, weight=1)
        file_frame.columnconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def log_message(self, message: str):
        """在日志区域显示消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def select_files(self):
        """选择票据文件"""
        file_types = [
            ("图像文件", "*.jpg *.jpeg *.png"),
            ("PDF文件", "*.pdf"),
            ("所有文件", "*.*")
        ]
        
        files = filedialog.askopenfilenames(
            title="选择票据文件",
            filetypes=file_types
        )
        
        if files:
            self.selected_files.extend(files)
            self.update_file_list()
            self.log_message(f"已选择 {len(files)} 个文件")
    
    def clear_files(self):
        """清空文件列表"""
        self.selected_files.clear()
        self.update_file_list()
        self.log_message("已清空文件列表")
    
    def update_file_list(self):
        """更新文件列表显示"""
        self.file_listbox.delete(0, tk.END)
        for file_path in self.selected_files:
            filename = os.path.basename(file_path)
            self.file_listbox.insert(tk.END, filename)
    
    def save_config(self):
        """保存配置"""
        config = {
            "feishu": {
                "app_id": self.app_id_var.get(),
                "app_secret": self.app_secret_var.get(),
                "app_token": self.app_token_var.get(),
                "table_id": self.table_id_var.get()
            },
            "ai": {
                "openai_key": self.openai_key_var.get()
            }
        }
        
        try:
            with open("user_config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            self.log_message("配置已保存")
            messagebox.showinfo("成功", "配置已保存到 user_config.json")
            
        except Exception as e:
            self.log_message(f"保存配置失败: {str(e)}")
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
    
    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists("user_config.json"):
                with open("user_config.json", "r", encoding="utf-8") as f:
                    config = json.load(f)
                
                # 加载飞书配置
                feishu_config = config.get("feishu", {})
                self.app_id_var.set(feishu_config.get("app_id", ""))
                self.app_secret_var.set(feishu_config.get("app_secret", ""))
                self.app_token_var.set(feishu_config.get("app_token", ""))
                self.table_id_var.set(feishu_config.get("table_id", ""))
                
                # 加载AI配置
                ai_config = config.get("ai", {})
                self.openai_key_var.set(ai_config.get("openai_key", ""))
                
                self.log_message("配置已加载")
            
        except Exception as e:
            self.log_message(f"加载配置失败: {str(e)}")
    
    def validate_config(self) -> bool:
        """验证配置是否完整"""
        if not self.app_id_var.get():
            messagebox.showerror("错误", "请填写飞书App ID")
            return False
        
        if not self.app_secret_var.get():
            messagebox.showerror("错误", "请填写飞书App Secret")
            return False
        
        if not self.app_token_var.get():
            messagebox.showerror("错误", "请填写表格App Token")
            return False
        
        if not self.table_id_var.get():
            messagebox.showerror("错误", "请填写表格ID")
            return False
        
        if not self.openai_key_var.get():
            messagebox.showerror("错误", "请填写OpenAI API Key")
            return False
        
        return True
    
    def start_processing(self):
        """开始处理票据"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择票据文件")
            return
        
        if not self.validate_config():
            return
        
        # 禁用处理按钮
        self.process_button.config(state="disabled")
        
        # 在新线程中处理
        thread = threading.Thread(target=self.process_tickets)
        thread.daemon = True
        thread.start()
    
    def process_tickets(self):
        """处理票据（在后台线程中运行）"""
        try:
            # 创建处理器
            self.processor = FeishuTicketProcessor(
                app_id=self.app_id_var.get(),
                app_secret=self.app_secret_var.get(),
                openai_api_key=self.openai_key_var.get()
            )
            
            total_files = len(self.selected_files)
            success_count = 0
            
            self.log_message(f"开始处理 {total_files} 个文件")
            
            for i, file_path in enumerate(self.selected_files):
                # 更新进度
                progress = (i / total_files) * 100
                self.progress_var.set(progress)
                
                filename = os.path.basename(file_path)
                self.log_message(f"正在处理: {filename}")
                
                # 处理单个文件
                success = self.processor.process_ticket(
                    file_path,
                    self.app_token_var.get(),
                    self.table_id_var.get()
                )
                
                if success:
                    success_count += 1
                    self.log_message(f"✓ {filename} 处理成功")
                else:
                    self.log_message(f"✗ {filename} 处理失败")
            
            # 完成处理
            self.progress_var.set(100)
            self.log_message(f"处理完成: {success_count}/{total_files} 成功")
            
            messagebox.showinfo("完成", f"处理完成\n成功: {success_count}\n总计: {total_files}")
            
        except Exception as e:
            self.log_message(f"处理异常: {str(e)}")
            messagebox.showerror("错误", f"处理异常: {str(e)}")
        
        finally:
            # 重新启用处理按钮
            self.process_button.config(state="normal")
            self.progress_var.set(0)
    
    def run(self):
        """运行GUI应用"""
        self.root.mainloop()


if __name__ == "__main__":
    app = TicketProcessorGUI()
    app.run()
