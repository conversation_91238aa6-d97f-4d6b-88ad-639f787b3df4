#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书票据处理系统 - GUI界面
"""

import sys
import os
import json
from pathlib import Path
from typing import List
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
from datetime import datetime
import tempfile
from PIL import Image, ImageTk
import io

from feishu_ticket_processor import FeishuTicketProcessor
from config import FEISHU_CONFIG, AI_CONFIG, APP_CONFIG

class TicketProcessorGUI:
    """票据处理系统GUI界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("飞书票据处理系统")
        self.root.geometry("800x600")
        
        # 初始化变量
        self.processor = None
        self.selected_files = []
        self.current_prompt = ""
        self.clipboard_images = []  # 存储剪贴板图像

        # 创建界面
        self.create_widgets()

        # 绑定剪贴板监听
        self.setup_clipboard_monitoring()

        # 加载配置
        self.load_config()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置设置", padding="5")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 飞书配置
        ttk.Label(config_frame, text="飞书App ID:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.app_id_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.app_id_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        ttk.Label(config_frame, text="飞书App Secret:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        self.app_secret_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.app_secret_var, width=40, show="*").grid(row=1, column=1, sticky=(tk.W, tk.E))
        
        ttk.Label(config_frame, text="表格App Token:").grid(row=2, column=0, sticky=tk.W, padx=(0, 5))
        self.app_token_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.app_token_var, width=40).grid(row=2, column=1, sticky=(tk.W, tk.E))
        
        ttk.Label(config_frame, text="表格ID:").grid(row=3, column=0, sticky=tk.W, padx=(0, 5))
        self.table_id_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.table_id_var, width=40).grid(row=3, column=1, sticky=(tk.W, tk.E))
        
        # AI配置
        ttk.Label(config_frame, text="AI API Key:").grid(row=4, column=0, sticky=tk.W, padx=(0, 5))
        self.ai_api_key_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.ai_api_key_var, width=40, show="*").grid(row=4, column=1, sticky=(tk.W, tk.E))

        ttk.Label(config_frame, text="AI API URL:").grid(row=5, column=0, sticky=tk.W, padx=(0, 5))
        self.ai_api_url_var = tk.StringVar()
        ai_url_entry = ttk.Entry(config_frame, textvariable=self.ai_api_url_var, width=40)
        ai_url_entry.grid(row=5, column=1, sticky=(tk.W, tk.E))

        # AI模型预设按钮
        preset_frame = ttk.Frame(config_frame)
        preset_frame.grid(row=6, column=1, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Button(preset_frame, text="OpenAI", command=self.set_openai_preset).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(preset_frame, text="文心一言", command=self.set_baidu_preset).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(preset_frame, text="通义千问", command=self.set_alibaba_preset).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(preset_frame, text="自定义", command=self.set_custom_preset).pack(side=tk.LEFT)

        # 保存配置和编辑提示词按钮
        button_config_frame = ttk.Frame(config_frame)
        button_config_frame.grid(row=7, column=1, sticky=tk.E, pady=(5, 0))

        ttk.Button(button_config_frame, text="编辑提示词", command=self.edit_prompt).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_config_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT)
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="票据文件 (支持文件选择、Ctrl+V粘贴图像)", padding="5")
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 文件选择按钮
        button_frame = ttk.Frame(file_frame)
        button_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E))

        ttk.Button(button_frame, text="选择票据文件", command=self.select_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="粘贴图像", command=self.paste_image).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="预览图像", command=self.preview_images).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空列表", command=self.clear_files).pack(side=tk.LEFT, padx=(0, 5))
        
        # 文件列表
        self.file_listbox = tk.Listbox(file_frame, height=6)
        self.file_listbox.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(file_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        scrollbar.grid(row=1, column=2, sticky=(tk.N, tk.S))
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        # 处理控制区域
        control_frame = ttk.LabelFrame(main_frame, text="处理控制", padding="5")
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 处理按钮
        self.process_button = ttk.Button(control_frame, text="开始处理", command=self.start_processing)
        self.process_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="5")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        config_frame.columnconfigure(1, weight=1)
        file_frame.columnconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def log_message(self, message: str):
        """在日志区域显示消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def set_openai_preset(self):
        """设置OpenAI预设"""
        self.ai_api_url_var.set("https://api.openai.com/v1/chat/completions")
        self.log_message("已设置OpenAI API预设")

    def set_baidu_preset(self):
        """设置百度文心一言预设"""
        self.ai_api_url_var.set("https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions")
        self.log_message("已设置百度文心一言API预设")

    def set_alibaba_preset(self):
        """设置阿里通义千问预设"""
        self.ai_api_url_var.set("https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation")
        self.log_message("已设置阿里通义千问API预设")

    def set_custom_preset(self):
        """设置自定义API"""
        self.ai_api_url_var.set("")
        self.log_message("请手动输入自定义API地址")

    def edit_prompt(self):
        """编辑提示词"""
        # 创建提示词编辑窗口
        prompt_window = tk.Toplevel(self.root)
        prompt_window.title("编辑提示词")
        prompt_window.geometry("800x600")
        prompt_window.transient(self.root)
        prompt_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(prompt_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 说明标签
        info_label = ttk.Label(main_frame, text="请编辑票据识别的提示词模板：")
        info_label.pack(anchor=tk.W, pady=(0, 5))

        # 提示词文本框
        prompt_text = scrolledtext.ScrolledText(main_frame, height=25, width=80)
        prompt_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 加载当前提示词
        if self.current_prompt:
            prompt_text.insert(tk.END, self.current_prompt)
        else:
            # 加载默认提示词
            default_prompt = self.get_default_prompt()
            prompt_text.insert(tk.END, default_prompt)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        def save_prompt():
            """保存提示词"""
            self.current_prompt = prompt_text.get("1.0", tk.END).strip()
            self.log_message("提示词已更新")
            prompt_window.destroy()

        def reset_prompt():
            """重置为默认提示词"""
            prompt_text.delete("1.0", tk.END)
            prompt_text.insert(tk.END, self.get_default_prompt())

        ttk.Button(button_frame, text="重置默认", command=reset_prompt).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="取消", command=prompt_window.destroy).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="保存", command=save_prompt).pack(side=tk.RIGHT)

    def get_default_prompt(self):
        """获取默认提示词"""
        return """你是一名专业的中文服饰票据信息抽取助手。请分析以下票据图像，提取关键信息并转换为标准JSON格式。

**分析要求：**
1. 仔细识别票据中的所有文字信息
2. 提取以下字段：
   - supplier：供应商名称（通常在票据顶部或印章处）
   - date：日期（统一格式为YYYY-MM-DD）
   - type：票据类型（任一商品数量或金额为负数则为"退货"，否则为"销售"）
   - items：商品明细数组，每项包含：
     * 款号：商品编号/货号
     * 颜色规格：颜色、尺码等规格信息
     * 数量：商品数量（退货为负数）
     * 单价：单位价格
     * 小计：该行总金额（数量×单价）

**注意事项：**
- 自动纠正识别错误（如"状号"→"款号"）
- 合理推断缺失信息（根据上下文判断供应商、日期）
- 确保数值计算正确（小计=数量×单价）
- 严格按JSON格式输出，不包含其他文字

请输出标准JSON结果："""

    def setup_clipboard_monitoring(self):
        """设置剪贴板监听"""
        # 绑定Ctrl+V快捷键
        self.root.bind('<Control-v>', self.on_paste_shortcut)
        self.root.focus_set()  # 确保窗口可以接收键盘事件

    def on_paste_shortcut(self, event):
        """处理Ctrl+V快捷键"""
        self.paste_image()
        return "break"  # 阻止事件继续传播

    def paste_image(self):
        """从剪贴板粘贴图像"""
        try:
            # 尝试从剪贴板获取图像
            image = ImageTk.getimage()
            if image:
                # 保存图像到临时文件
                temp_file = self.save_clipboard_image(image)
                if temp_file:
                    self.selected_files.append(temp_file)
                    self.clipboard_images.append(temp_file)
                    self.update_file_list()
                    self.log_message(f"已粘贴图像: {os.path.basename(temp_file)}")
                else:
                    self.log_message("保存剪贴板图像失败")
            else:
                # 尝试其他方法获取剪贴板图像
                self.paste_image_alternative()
        except Exception as e:
            self.log_message(f"粘贴图像失败: {str(e)}")
            # 尝试备用方法
            self.paste_image_alternative()

    def paste_image_alternative(self):
        """备用的图像粘贴方法"""
        try:
            # 使用PIL直接从剪贴板获取图像
            from PIL import ImageGrab
            image = ImageGrab.grabclipboard()

            if image:
                # 保存图像到临时文件
                temp_file = self.save_clipboard_image(image)
                if temp_file:
                    self.selected_files.append(temp_file)
                    self.clipboard_images.append(temp_file)
                    self.update_file_list()
                    self.log_message(f"已粘贴图像: {os.path.basename(temp_file)}")
                else:
                    self.log_message("保存剪贴板图像失败")
            else:
                messagebox.showinfo("提示", "剪贴板中没有图像\n请先复制图像后再粘贴")

        except ImportError:
            messagebox.showerror("错误", "缺少PIL.ImageGrab模块\n请安装完整的Pillow包")
        except Exception as e:
            self.log_message(f"备用粘贴方法失败: {str(e)}")
            messagebox.showerror("错误", f"粘贴图像失败: {str(e)}")

    def save_clipboard_image(self, image):
        """保存剪贴板图像到临时文件"""
        try:
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_filename = f"clipboard_image_{timestamp}.png"
            temp_path = os.path.join(temp_dir, temp_filename)

            # 保存图像
            if hasattr(image, 'save'):
                image.save(temp_path, 'PNG')
            else:
                # 如果不是PIL图像，尝试转换
                pil_image = Image.fromarray(image)
                pil_image.save(temp_path, 'PNG')

            return temp_path

        except Exception as e:
            self.log_message(f"保存图像失败: {str(e)}")
            return None

    def preview_images(self):
        """预览选中的图像"""
        if not self.selected_files:
            messagebox.showinfo("提示", "请先选择或粘贴图像文件")
            return

        # 创建预览窗口
        preview_window = tk.Toplevel(self.root)
        preview_window.title("图像预览")
        preview_window.geometry("600x500")
        preview_window.transient(self.root)

        # 创建滚动框架
        canvas = tk.Canvas(preview_window)
        scrollbar = ttk.Scrollbar(preview_window, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 显示图像
        for i, file_path in enumerate(self.selected_files):
            try:
                if file_path.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                    # 加载和缩放图像
                    image = Image.open(file_path)
                    # 缩放图像以适应预览
                    image.thumbnail((200, 200), Image.Resampling.LANCZOS)
                    photo = ImageTk.PhotoImage(image)

                    # 创建图像标签
                    frame = ttk.Frame(scrollable_frame)
                    frame.pack(fill=tk.X, padx=5, pady=5)

                    # 文件名标签
                    name_label = ttk.Label(frame, text=f"{i+1}. {os.path.basename(file_path)}")
                    name_label.pack(anchor=tk.W)

                    # 图像标签
                    img_label = tk.Label(frame, image=photo)
                    img_label.image = photo  # 保持引用
                    img_label.pack(anchor=tk.W, pady=(5, 0))

            except Exception as e:
                # 如果不是图像文件或加载失败，显示文件名
                frame = ttk.Frame(scrollable_frame)
                frame.pack(fill=tk.X, padx=5, pady=5)

                name_label = ttk.Label(frame, text=f"{i+1}. {os.path.basename(file_path)} (无法预览)")
                name_label.pack(anchor=tk.W)

        # 布局滚动组件
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def select_files(self):
        """选择票据文件"""
        file_types = [
            ("图像文件", "*.jpg *.jpeg *.png"),
            ("PDF文件", "*.pdf"),
            ("所有文件", "*.*")
        ]
        
        files = filedialog.askopenfilenames(
            title="选择票据文件",
            filetypes=file_types
        )
        
        if files:
            self.selected_files.extend(files)
            self.update_file_list()
            self.log_message(f"已选择 {len(files)} 个文件")
    
    def clear_files(self):
        """清空文件列表"""
        # 清理临时图像文件
        for temp_file in self.clipboard_images:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    self.log_message(f"已删除临时文件: {os.path.basename(temp_file)}")
            except Exception as e:
                self.log_message(f"删除临时文件失败: {str(e)}")

        self.selected_files.clear()
        self.clipboard_images.clear()
        self.update_file_list()
        self.log_message("已清空文件列表")
    
    def update_file_list(self):
        """更新文件列表显示"""
        self.file_listbox.delete(0, tk.END)
        for file_path in self.selected_files:
            filename = os.path.basename(file_path)
            # 区分粘贴的图像和普通文件
            if file_path in self.clipboard_images:
                display_name = f"📋 {filename}"  # 添加剪贴板图标
            else:
                display_name = f"📁 {filename}"  # 添加文件图标
            self.file_listbox.insert(tk.END, display_name)
    
    def save_config(self):
        """保存配置"""
        config = {
            "feishu": {
                "app_id": self.app_id_var.get(),
                "app_secret": self.app_secret_var.get(),
                "app_token": self.app_token_var.get(),
                "table_id": self.table_id_var.get()
            },
            "ai": {
                "api_key": self.ai_api_key_var.get(),
                "api_url": self.ai_api_url_var.get()
            },
            "prompt": self.current_prompt
        }

        try:
            with open("user_config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            self.log_message("配置已保存")
            messagebox.showinfo("成功", "配置已保存到 user_config.json")

        except Exception as e:
            self.log_message(f"保存配置失败: {str(e)}")
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
    
    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists("user_config.json"):
                with open("user_config.json", "r", encoding="utf-8") as f:
                    config = json.load(f)

                # 加载飞书配置
                feishu_config = config.get("feishu", {})
                self.app_id_var.set(feishu_config.get("app_id", ""))
                self.app_secret_var.set(feishu_config.get("app_secret", ""))
                self.app_token_var.set(feishu_config.get("app_token", ""))
                self.table_id_var.set(feishu_config.get("table_id", ""))

                # 加载AI配置
                ai_config = config.get("ai", {})
                self.ai_api_key_var.set(ai_config.get("api_key", ""))
                self.ai_api_url_var.set(ai_config.get("api_url", ""))

                # 加载提示词
                self.current_prompt = config.get("prompt", "")

                self.log_message("配置已加载")

        except Exception as e:
            self.log_message(f"加载配置失败: {str(e)}")
    
    def validate_config(self) -> bool:
        """验证配置是否完整"""
        if not self.app_id_var.get():
            messagebox.showerror("错误", "请填写飞书App ID")
            return False

        if not self.app_secret_var.get():
            messagebox.showerror("错误", "请填写飞书App Secret")
            return False

        if not self.app_token_var.get():
            messagebox.showerror("错误", "请填写表格App Token")
            return False

        if not self.table_id_var.get():
            messagebox.showerror("错误", "请填写表格ID")
            return False

        if not self.ai_api_key_var.get():
            messagebox.showerror("错误", "请填写AI API Key")
            return False

        if not self.ai_api_url_var.get():
            messagebox.showerror("错误", "请填写AI API URL")
            return False

        return True
    
    def start_processing(self):
        """开始处理票据"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择票据文件")
            return
        
        if not self.validate_config():
            return
        
        # 禁用处理按钮
        self.process_button.config(state="disabled")
        
        # 在新线程中处理
        thread = threading.Thread(target=self.process_tickets)
        thread.daemon = True
        thread.start()
    
    def process_tickets(self):
        """处理票据（在后台线程中运行）"""
        try:
            # 创建处理器
            self.processor = FeishuTicketProcessor(
                app_id=self.app_id_var.get(),
                app_secret=self.app_secret_var.get(),
                ai_api_key=self.ai_api_key_var.get(),
                ai_api_url=self.ai_api_url_var.get(),
                prompt_template=self.current_prompt
            )
            
            total_files = len(self.selected_files)
            success_count = 0
            
            self.log_message(f"开始处理 {total_files} 个文件")
            
            for i, file_path in enumerate(self.selected_files):
                # 更新进度
                progress = (i / total_files) * 100
                self.progress_var.set(progress)
                
                filename = os.path.basename(file_path)
                self.log_message(f"正在处理: {filename}")
                
                # 处理单个文件
                success = self.processor.process_ticket(
                    file_path,
                    self.app_token_var.get(),
                    self.table_id_var.get()
                )
                
                if success:
                    success_count += 1
                    self.log_message(f"✓ {filename} 处理成功")
                else:
                    self.log_message(f"✗ {filename} 处理失败")
            
            # 完成处理
            self.progress_var.set(100)
            self.log_message(f"处理完成: {success_count}/{total_files} 成功")
            
            messagebox.showinfo("完成", f"处理完成\n成功: {success_count}\n总计: {total_files}")
            
        except Exception as e:
            self.log_message(f"处理异常: {str(e)}")
            messagebox.showerror("错误", f"处理异常: {str(e)}")
        
        finally:
            # 重新启用处理按钮
            self.process_button.config(state="normal")
            self.progress_var.set(0)
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        for temp_file in self.clipboard_images:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except Exception:
                pass  # 忽略清理错误

    def on_closing(self):
        """应用关闭时的处理"""
        self.cleanup_temp_files()
        self.root.destroy()

    def run(self):
        """运行GUI应用"""
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


if __name__ == "__main__":
    app = TicketProcessorGUI()
    app.run()
