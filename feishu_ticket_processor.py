#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书票据处理系统
使用AI模型识别票据信息并自动提交到飞书多维表格
"""

import json
import base64
import datetime
from typing import Dict, List, Optional
import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *
import requests
from PIL import Image
import io

class FeishuTicketProcessor:
    """飞书票据处理器"""

    def __init__(self, app_id: str, app_secret: str, ai_api_key: str = "", ai_api_url: str = "", prompt_template: str = ""):
        """
        初始化处理器

        Args:
            app_id: 飞书应用ID
            app_secret: 飞书应用密钥
            ai_api_key: AI模型API密钥
            ai_api_url: AI模型API地址
            prompt_template: 自定义提示词模板
        """
        # 初始化飞书客户端
        self.feishu_client = lark.Client.builder() \
            .app_id(app_id) \
            .app_secret(app_secret) \
            .build()

        # AI模型配置
        self.ai_api_key = ai_api_key
        self.ai_api_url = ai_api_url

        # 票据解析提示词模板（可自定义）
        self.prompt_template = prompt_template or self.get_default_prompt()

    def get_default_prompt(self) -> str:
        """获取默认提示词模板"""
        return """你是一名专业的中文服饰票据信息抽取助手。请分析以下票据图像，提取关键信息并转换为标准JSON格式。

**分析要求：**
1. 仔细识别票据中的所有文字信息
2. 提取以下字段：
   - supplier：供应商名称（通常在票据顶部或印章处）
   - date：日期（统一格式为YYYY-MM-DD）
   - type：票据类型（任一商品数量或金额为负数则为"退货"，否则为"销售"）
   - items：商品明细数组，每项包含：
     * 款号：商品编号/货号
     * 颜色规格：颜色、尺码等规格信息
     * 数量：商品数量（退货为负数）
     * 单价：单位价格
     * 小计：该行总金额（数量×单价）

**注意事项：**
- 自动纠正识别错误（如"状号"→"款号"）
- 合理推断缺失信息（根据上下文判断供应商、日期）
- 确保数值计算正确（小计=数量×单价）
- 严格按JSON格式输出，不包含其他文字

请输出标准JSON结果："""

    def update_ai_config(self, api_key: str, api_url: str, prompt_template: str = None):
        """
        更新AI模型配置

        Args:
            api_key: API密钥
            api_url: API地址
            prompt_template: 提示词模板（可选）
        """
        self.ai_api_key = api_key
        self.ai_api_url = api_url
        if prompt_template:
            self.prompt_template = prompt_template

    def encode_image(self, image_path: str) -> str:
        """将图像编码为base64格式"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def analyze_ticket_with_ai(self, image_path: str) -> Dict:
        """
        使用AI模型分析票据图像

        Args:
            image_path: 票据图像路径

        Returns:
            解析后的票据数据字典
        """
        try:
            # 编码图像
            base64_image = self.encode_image(image_path)

            # 构建请求数据
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.ai_api_key}"
            }

            # 通用的多模态请求格式
            payload = {
                "model": "gpt-4-vision-preview",  # 默认模型，可根据API调整
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": self.prompt_template},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.1
            }

            # 发送API请求
            response = requests.post(
                self.ai_api_url,
                headers=headers,
                json=payload,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                # 解析AI返回的JSON结果
                ai_response = result.get("choices", [{}])[0].get("message", {}).get("content", "")

                # 尝试提取JSON部分
                ai_response = ai_response.strip()
                if ai_response.startswith("```json"):
                    ai_response = ai_response[7:-3].strip()
                elif ai_response.startswith("```"):
                    ai_response = ai_response[3:-3].strip()

                ticket_data = json.loads(ai_response)
                return ticket_data
            else:
                print(f"API请求失败: {response.status_code} - {response.text}")
                return None

        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {str(e)}")
            print(f"AI返回内容: {ai_response}")
            return None
        except Exception as e:
            print(f"AI分析失败: {str(e)}")
            return None

    def create_feishu_table(self, app_token: str, table_name: str) -> str:
        """
        创建飞书多维表格
        
        Args:
            app_token: 多维表格应用token
            table_name: 表格名称
            
        Returns:
            创建的表格ID
        """
        try:
            # 定义表格字段
            fields = [
                {"field_name": "票据ID", "type": 1},  # 文本
                {"field_name": "供应商", "type": 1},   # 文本
                {"field_name": "日期", "type": 5},     # 日期
                {"field_name": "类型", "type": 3},     # 单选
                {"field_name": "款号", "type": 1},     # 文本
                {"field_name": "颜色规格", "type": 1}, # 文本
                {"field_name": "数量", "type": 2},     # 数字
                {"field_name": "单价", "type": 2},     # 数字
                {"field_name": "小计", "type": 2},     # 数字
                {"field_name": "处理时间", "type": 4}, # 日期时间
                {"field_name": "状态", "type": 3},     # 单选
            ]
            
            # 创建表格请求
            request = CreateAppTableRequest.builder() \
                .app_token(app_token) \
                .request_body(CreateAppTableRequestBody.builder()
                    .table(AppTable.builder()
                        .name(table_name)
                        .default_view_name("默认视图")
                        .fields(fields)
                        .build())
                    .build()) \
                .build()
            
            # 发送请求
            response = self.feishu_client.bitable.v1.app_table.create(request)
            
            if response.success():
                return response.data.table_id
            else:
                print(f"创建表格失败: {response.msg}")
                return None
                
        except Exception as e:
            print(f"创建表格异常: {str(e)}")
            return None

    def submit_to_feishu(self, table_id: str, ticket_data: Dict) -> bool:
        """
        将票据数据提交到飞书电子表格

        Args:
            table_id: 电子表格ID
            ticket_data: 票据数据

        Returns:
            提交是否成功
        """
        try:
            # 生成唯一票据ID
            ticket_id = f"T{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
            current_time = datetime.datetime.now().isoformat()

            # 构建要插入的数据行
            rows_data = []
            for item in ticket_data.get('items', []):
                row_data = [
                    ticket_id,  # 票据ID
                    ticket_data.get('supplier', ''),  # 供应商
                    ticket_data.get('date', ''),  # 日期
                    ticket_data.get('type', '销售'),  # 类型
                    item.get('款号', ''),  # 款号
                    item.get('颜色规格', ''),  # 颜色规格
                    item.get('数量', 0),  # 数量
                    item.get('单价', 0.0),  # 单价
                    item.get('小计', 0.0),  # 小计
                    current_time,  # 处理时间
                    "待审核"  # 状态
                ]
                rows_data.append(row_data)

            # 使用飞书电子表格API插入数据
            # 这里需要根据实际的飞书电子表格API来实现
            # 由于电子表格API与多维表格API不同，这里提供一个通用的HTTP请求方式

            headers = {
                "Authorization": f"Bearer {self.feishu_client.get_tenant_access_token()}",
                "Content-Type": "application/json"
            }

            # 构建请求数据
            payload = {
                "valueRange": {
                    "range": f"{table_id}!A:K",  # 假设有11列数据
                    "values": rows_data
                }
            }

            # 发送请求到飞书电子表格API
            api_url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{table_id}/values_append"

            response = requests.post(api_url, headers=headers, json=payload)

            if response.status_code == 200:
                print(f"成功提交 {len(ticket_data.get('items', []))} 条记录到飞书电子表格")
                return True
            else:
                print(f"提交记录失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"提交数据异常: {str(e)}")
            return False

    def process_ticket(self, image_path: str, table_id: str) -> bool:
        """
        处理单张票据的完整流程

        Args:
            image_path: 票据图像路径
            table_id: 飞书电子表格ID

        Returns:
            处理是否成功
        """
        print(f"开始处理票据: {image_path}")
        
        # 1. AI识别票据
        ticket_data = self.analyze_ticket_with_ai(image_path)
        if not ticket_data:
            print("票据识别失败")
            return False
        
        print(f"识别成功，供应商: {ticket_data.get('supplier', '未知')}")
        print(f"商品数量: {len(ticket_data.get('items', []))}")
        
        # 2. 提交到飞书表格
        success = self.submit_to_feishu(table_id, ticket_data)
        
        if success:
            print("票据处理完成")
        else:
            print("提交飞书失败")
            
        return success

    def batch_process_tickets(self, image_paths: List[str], table_id: str):
        """
        批量处理多张票据

        Args:
            image_paths: 票据图像路径列表
            table_id: 飞书电子表格ID
        """
        success_count = 0
        total_count = len(image_paths)
        
        print(f"开始批量处理 {total_count} 张票据")
        
        for i, image_path in enumerate(image_paths, 1):
            print(f"\n处理进度: {i}/{total_count}")
            
            if self.process_ticket(image_path, table_id):
                success_count += 1
        
        print(f"\n批量处理完成: {success_count}/{total_count} 成功")


# 使用示例
if __name__ == "__main__":
    # 配置信息
    FEISHU_APP_ID = "your_feishu_app_id"
    FEISHU_APP_SECRET = "your_feishu_app_secret"
    OPENAI_API_KEY = "your_openai_api_key"
    
    # 飞书电子表格信息
    TABLE_ID = "your_table_id"    # 电子表格ID
    
    # 创建处理器
    processor = FeishuTicketProcessor(
        app_id=FEISHU_APP_ID,
        app_secret=FEISHU_APP_SECRET,
        openai_api_key=OPENAI_API_KEY
    )
    
    # 处理单张票据
    # processor.process_ticket("ticket1.jpg", TABLE_ID)

    # 批量处理票据
    # ticket_images = ["ticket1.jpg", "ticket2.jpg", "ticket3.jpg"]
    # processor.batch_process_tickets(ticket_images, TABLE_ID)
